# Kafka Spring Boot Application

A refactored Spring Boot application demonstrating Kafka producer and consumer functionality with improved architecture and best practices.

## Architecture Overview

The application has been refactored to follow Spring Boot best practices with clear separation of concerns:

### Package Structure
```
src/main/java/com/example/kafka_spring_boot/
├── KafkaSpringBootApplication.java     # Main application class
├── config/
│   ├── KafkaConfig.java               # Kafka configuration
│   └── KafkaProperties.java           # Configuration properties
└── service/
    ├── ApplicationRunnerService.java   # Command line runner
    ├── KafkaConsumerService.java      # Kafka consumer logic
    └── KafkaProducerService.java      # Kafka producer logic
```

### Key Improvements

1. **Separation of Concerns**: Each class has a single responsibility
2. **Configuration Management**: Externalized configuration using `@ConfigurationProperties`
3. **Error Handling**: Proper exception handling and logging
4. **Testability**: Services are easily testable with dependency injection
5. **Maintainability**: Clean code structure with clear interfaces

## Configuration

The application uses `application.yaml` for configuration:

```yaml
# Spring Kafka configuration
spring:
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: kafka-spring-boot
      auto-offset-reset: earliest
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

# Application-specific configuration
app:
  kafka:
    topic-name: my-topic
    message-count: 1000
    default-key: key
    default-value: value
```

## Usage

### Prerequisites
- Java 17+
- Apache Kafka running on localhost:9092

### Running the Application

1. **Producer Mode**: Send messages to Kafka
   ```bash
   ./gradlew bootRun --args="--producer"
   ```

2. **Consumer Mode**: Consume messages from Kafka
   ```bash
   ./gradlew bootRun --args="--consumer"
   ```

3. **Both Producer and Consumer**:
   ```bash
   ./gradlew bootRun --args="--producer --consumer"
   ```

4. **Help**:
   ```bash
   ./gradlew bootRun --args="--help"
   ```

### Building the Application

```bash
./gradlew build
```

### Running Tests

```bash
./gradlew test
```

## Features

### KafkaProducerService
- Sends messages to configured Kafka topic
- Supports batch message sending
- Comprehensive error handling and logging
- Configurable message count and content

### KafkaConsumerService
- Consumes messages from configured Kafka topic
- Graceful start/stop functionality
- Detailed message logging with metadata
- Error handling for message processing

### ApplicationRunnerService
- Command line argument processing
- Orchestrates producer and consumer operations
- Graceful shutdown handling
- User-friendly help and usage information

## Testing

The application includes comprehensive tests:

- **Unit Tests**: For individual service components
- **Integration Tests**: For Spring Boot context and Kafka integration
- **Embedded Kafka**: For testing without external dependencies

## Configuration Properties

| Property | Default | Description |
|----------|---------|-------------|
| `app.kafka.topic-name` | `my-topic` | Kafka topic name |
| `app.kafka.message-count` | `1000` | Number of messages to send |
| `app.kafka.default-key` | `key` | Default message key |
| `app.kafka.default-value` | `value` | Default message value |

## Logging

The application uses structured logging with configurable levels:
- Application logs: `INFO` level
- Spring Kafka logs: `WARN` level
- Apache Kafka logs: `WARN` level

## Error Handling

- Comprehensive exception handling in all services
- Graceful degradation for Kafka connectivity issues
- Detailed error logging for troubleshooting
- Proper resource cleanup on shutdown

## Future Enhancements

- Add support for different message serialization formats (JSON, Avro)
- Implement dead letter queue for failed messages
- Add metrics and monitoring capabilities
- Support for multiple topics and consumer groups
- Configuration for different environments (dev, staging, prod)

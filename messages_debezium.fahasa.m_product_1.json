[{"topic": "debezium.fahasa.m_product", "partition": 0, "offset": 1, "timestamp": 1748342552016, "timestampType": "CREATE_TIME", "headers": [], "key": {"schema": {"type": "struct", "fields": [{"type": "int64", "optional": false, "field": "m_product_id"}], "optional": false, "name": "debezium.fahasa.m_product.Key"}, "payload": {"m_product_id": 2047523}}, "value": {"schema": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "int64", "optional": false, "field": "m_product_id"}, {"type": "string", "optional": true, "field": "value"}, {"type": "string", "optional": true, "field": "name"}, {"type": "int64", "optional": true, "field": "m_attributesetinstance_id"}], "optional": true, "name": "debezium.fahasa.m_product.Value", "field": "before"}, {"type": "struct", "fields": [{"type": "int64", "optional": false, "field": "m_product_id"}, {"type": "string", "optional": true, "field": "value"}, {"type": "string", "optional": true, "field": "name"}, {"type": "int64", "optional": true, "field": "m_attributesetinstance_id"}], "optional": true, "name": "debezium.fahasa.m_product.Value", "field": "after"}, {"type": "struct", "fields": [{"type": "string", "optional": false, "field": "version"}, {"type": "string", "optional": false, "field": "connector"}, {"type": "string", "optional": false, "field": "name"}, {"type": "int64", "optional": false, "field": "ts_ms"}, {"type": "string", "optional": true, "name": "io.debezium.data.Enum", "version": 1, "parameters": {"allowed": "true,last,false,incremental"}, "default": "false", "field": "snapshot"}, {"type": "string", "optional": false, "field": "db"}, {"type": "string", "optional": true, "field": "sequence"}, {"type": "string", "optional": false, "field": "schema"}, {"type": "string", "optional": false, "field": "table"}, {"type": "int64", "optional": true, "field": "txId"}, {"type": "int64", "optional": true, "field": "lsn"}, {"type": "int64", "optional": true, "field": "xmin"}], "optional": false, "name": "io.debezium.connector.postgresql.Source", "field": "source"}, {"type": "string", "optional": false, "field": "op"}, {"type": "int64", "optional": true, "field": "ts_ms"}, {"type": "struct", "fields": [{"type": "string", "optional": false, "field": "id"}, {"type": "int64", "optional": false, "field": "total_order"}, {"type": "int64", "optional": false, "field": "data_collection_order"}], "optional": true, "name": "event.block", "version": 1, "field": "transaction"}], "optional": false, "name": "debezium.fahasa.m_product.Envelope", "version": 1}, "payload": {"before": null, "after": {"m_product_id": 2047523, "value": "3900000295475", "name": "CDC Pipeline Test - 2025-05-27 17:42:31", "m_attributesetinstance_id": 2002401}, "source": {"version": "2.4.2.Final", "connector": "postgresql", "name": "debezium", "ts_ms": 1748342551485, "snapshot": "false", "db": "postgres", "sequence": "[\"120498728\",\"120523840\"]", "schema": "fahasa", "table": "m_product", "txId": 871, "lsn": 120523840, "xmin": null}, "op": "u", "ts_ms": 1748342551802, "transaction": null}}}]
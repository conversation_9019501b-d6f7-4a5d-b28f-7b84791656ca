# Spring Boot configuration
spring:
  application:
    name: kafka-spring-boot

# Spring Kafka configuration
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: kafka-spring-boot
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
# Application-specific configuration
app:
  kafka:
    topic-name: my-topic
    message-count: 1000
    default-key: key
    default-value: value
    debezium:
        fahasa:
            m_product:
                topic-name: debezium-fahasa-m_product

# Logging configuration
logging:
  level:
    '[com.example.kafka_spring_boot]': INFO
    '[org.springframework.kafka]': WARN
    '[org.apache.kafka]': WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

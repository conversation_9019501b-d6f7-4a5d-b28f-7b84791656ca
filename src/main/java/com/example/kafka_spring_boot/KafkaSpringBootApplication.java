package com.example.kafka_spring_boot;

import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

@Service
class Producer {
	// Implement Kafka message production logic here
	private static final Logger logger = LoggerFactory.getLogger(Producer.class);
	private static final String TOPIC = "my-topic";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	public void sendMessage(String key, String value) {
		CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(TOPIC, key, value);

		future.whenComplete((result, ex) -> {
			if (ex == null) {
				logger.info(String.format("Produced event to topic %s: key = %-10s value = %s",
						result.getRecordMetadata().topic(), key, value));
			} else {
				ex.printStackTrace(System.out);
			}
		});
	}
}

@Service
class Consumer {
	@KafkaListener(topics = "my-topic", groupId = "compose-connect-group", id = "myConsumer", autoStartup = "false")
	public void listen(String message) {
		System.out.println("Received message: " + message);
	}
}

@SpringBootApplication
public class KafkaSpringBootApplication {

	@Autowired
	private final Producer producer;

	public static void main(String[] args) {
		SpringApplication application = new SpringApplication(KafkaSpringBootApplication.class);
		application.setWebApplicationType(WebApplicationType.NONE);
		application.run(args);
	}

	@Bean
	public CommandLineRunner CommandLineRunnerBean() {
		return (args) -> {
			for (String arg : args) {
				switch (arg) {
					case "--producer":
						this.producer.sendMessage("key", "value");
						break;
					case "--consumer":
						MessageListenerContainer listenerContainer = kafkaListenerEndpointRegistry
								.getListenerContainer("myConsumer");
						listenerContainer.start();
						break;
					default:
						System.out.println("Unknown argument: " + arg);
						break;
				}
			}
		};
	}

	KafkaSpringBootApplication(Producer producer) {
		this.producer = producer;
	}

	@Autowired
	private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;
}

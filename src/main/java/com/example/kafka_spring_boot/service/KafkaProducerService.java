package com.example.kafka_spring_boot.service;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import com.example.kafka_spring_boot.config.KafkaProperties;

/**
 * Service for producing messages to Kafka topics.
 */
@Service
public class KafkaProducerService {

    private static final Logger logger = LoggerFactory.getLogger(KafkaProducerService.class);

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final KafkaProperties kafkaProperties;

    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplate, 
                               KafkaProperties kafkaProperties) {
        this.kafkaTemplate = kafkaTemplate;
        this.kafkaProperties = kafkaProperties;
    }

    /**
     * Sends a message to the configured Kafka topic.
     *
     * @param key   the message key
     * @param value the message value
     */
    public void sendMessage(String key, String value) {
        try {
            CompletableFuture<SendResult<String, String>> future = 
                kafkaTemplate.send(kafkaProperties.topicName(), key, value);

            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    logger.info("Produced event to topic {}: key = {} value = {}", 
                               result.getRecordMetadata().topic(), key, value);
                } else {
                    logger.error("Failed to produce event to topic {}: key = {} value = {}", 
                                kafkaProperties.topicName(), key, value, ex);
                }
            });
        } catch (Exception e) {
            logger.error("Error sending message to Kafka", e);
            throw new RuntimeException("Failed to send message to Kafka", e);
        }
    }

    /**
     * Sends multiple messages with default key-value pairs.
     *
     * @param count the number of messages to send
     */
    public void sendMessages(int count) {
        logger.info("Starting to send {} messages", count);
        
        for (int i = 0; i < count; i++) {
            String key = kafkaProperties.defaultKey() + "-" + i;
            String value = kafkaProperties.defaultValue() + "-" + i;
            sendMessage(key, value);
        }
        
        logger.info("Finished sending {} messages", count);
    }

    /**
     * Sends the configured number of default messages.
     */
    public void sendDefaultMessages() {
        sendMessages(kafkaProperties.messageCount());
    }
}

package com.example.kafka_spring_boot.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import com.example.kafka_spring_boot.config.KafkaProperties;

/**
 * Service for consuming messages from Kafka topics.
 */
@Service
public class KafkaConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(KafkaConsumerService.class);
    private static final String LISTENER_ID = "kafkaConsumer";

    private final KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;
    private final KafkaProperties kafkaProperties;

    public KafkaConsumerService(KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry,
                               KafkaProperties kafkaProperties) {
        this.kafkaListenerEndpointRegistry = kafkaListenerEndpointRegistry;
        this.kafkaProperties = kafkaProperties;
    }

    /**
     * Kafka listener method that processes incoming messages.
     *
     * @param message the message payload
     * @param key     the message key
     * @param topic   the topic name
     * @param partition the partition number
     * @param offset  the message offset
     */
    @KafkaListener(
        topics = "debezium.fahasa.m_product",
        groupId = "${spring.kafka.consumer.group-id}",
        id = LISTENER_ID,
        autoStartup = "false"
    )
    public void listen(@Payload String message,
                      @Header(KafkaHeaders.RECEIVED_KEY) String key,
                      @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                      @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                      @Header(KafkaHeaders.OFFSET) long offset) {

        try {
            logger.info("Consumed event from topic {}: key = {} value = {} partition = {} offset = {}",
                       topic, key, message, partition, offset);

            // Process the message here
            processMessage(key, message);

        } catch (Exception e) {
            logger.error("Error processing message: key = {} value = {}", key, message, e);
            // In a real application, you might want to send to a dead letter queue
        }
    }

    /**
     * Starts the Kafka consumer.
     */
    public void startConsumer() {
        try {
            MessageListenerContainer listenerContainer =
                kafkaListenerEndpointRegistry.getListenerContainer(LISTENER_ID);

            if (listenerContainer != null) {
                if (!listenerContainer.isRunning()) {
                    listenerContainer.start();
                    logger.info("Kafka consumer started successfully");
                } else {
                    logger.info("Kafka consumer is already running");
                }
            } else {
                logger.error("Kafka listener container not found with id: {}", LISTENER_ID);
            }
        } catch (Exception e) {
            logger.error("Failed to start Kafka consumer", e);
            throw new RuntimeException("Failed to start Kafka consumer", e);
        }
    }

    /**
     * Stops the Kafka consumer.
     */
    public void stopConsumer() {
        try {
            MessageListenerContainer listenerContainer =
                kafkaListenerEndpointRegistry.getListenerContainer(LISTENER_ID);

            if (listenerContainer != null && listenerContainer.isRunning()) {
                listenerContainer.stop();
                logger.info("Kafka consumer stopped successfully");
            } else {
                logger.info("Kafka consumer is not running");
            }
        } catch (Exception e) {
            logger.error("Failed to stop Kafka consumer", e);
        }
    }

    /**
     * Checks if the consumer is currently running.
     *
     * @return true if the consumer is running, false otherwise
     */
    public boolean isConsumerRunning() {
        MessageListenerContainer listenerContainer =
            kafkaListenerEndpointRegistry.getListenerContainer(LISTENER_ID);
        return listenerContainer != null && listenerContainer.isRunning();
    }

    /**
     * Process the consumed message. Override this method for custom processing logic.
     *
     * @param key     the message key
     * @param message the message value
     */
    protected void processMessage(String key, String message) {
        // Default implementation - just log the message
        // In a real application, you would implement your business logic here
        logger.debug("Processing message: key = {} value = {}", key, message);
    }
}

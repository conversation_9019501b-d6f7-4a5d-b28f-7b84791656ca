package com.example.kafka_spring_boot.service;

import java.util.Arrays;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

/**
 * Service that handles command line arguments and orchestrates the application flow.
 */
@Service
public class ApplicationRunnerService implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationRunnerService.class);
    
    private static final String PRODUCER_ARG = "--producer";
    private static final String CONSUMER_ARG = "--consumer";
    private static final String HELP_ARG = "--help";

    private final KafkaProducerService producerService;
    private final KafkaConsumerService consumerService;

    public ApplicationRunnerService(KafkaProducerService producerService, 
                                   KafkaConsumerService consumerService) {
        this.producerService = producerService;
        this.consumerService = consumerService;
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("Application started with arguments: {}", Arrays.toString(args));

        if (args.length == 0) {
            printUsage();
            return;
        }

        Set<String> arguments = Set.of(args);

        try {
            if (arguments.contains(HELP_ARG)) {
                printUsage();
                return;
            }

            if (arguments.contains(PRODUCER_ARG)) {
                runProducer();
            }

            if (arguments.contains(CONSUMER_ARG)) {
                runConsumer();
            }

            // Check for unknown arguments
            for (String arg : args) {
                if (!isValidArgument(arg)) {
                    logger.warn("Unknown argument: {}", arg);
                }
            }

        } catch (Exception e) {
            logger.error("Error during application execution", e);
            throw e;
        }

        logger.info("Application execution completed");
    }

    /**
     * Runs the Kafka producer to send messages.
     */
    private void runProducer() {
        logger.info("Starting producer mode");
        try {
            producerService.sendDefaultMessages();
            logger.info("Producer completed successfully");
        } catch (Exception e) {
            logger.error("Producer failed", e);
            throw new RuntimeException("Producer execution failed", e);
        }
    }

    /**
     * Runs the Kafka consumer to receive messages.
     */
    private void runConsumer() {
        logger.info("Starting consumer mode");
        try {
            consumerService.startConsumer();
            logger.info("Consumer started successfully. Press Ctrl+C to stop.");
            
            // Keep the application running to consume messages
            // In a real application, you might want to implement graceful shutdown
            addShutdownHook();
            
        } catch (Exception e) {
            logger.error("Consumer failed", e);
            throw new RuntimeException("Consumer execution failed", e);
        }
    }

    /**
     * Adds a shutdown hook to gracefully stop the consumer.
     */
    private void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("Shutdown signal received, stopping consumer...");
            try {
                consumerService.stopConsumer();
                logger.info("Consumer stopped gracefully");
            } catch (Exception e) {
                logger.error("Error during consumer shutdown", e);
            }
        }));
    }

    /**
     * Checks if the given argument is valid.
     *
     * @param arg the argument to check
     * @return true if valid, false otherwise
     */
    private boolean isValidArgument(String arg) {
        return PRODUCER_ARG.equals(arg) || 
               CONSUMER_ARG.equals(arg) || 
               HELP_ARG.equals(arg);
    }

    /**
     * Prints usage information.
     */
    private void printUsage() {
        System.out.println("\nKafka Spring Boot Application");
        System.out.println("=============================");
        System.out.println("Usage: java -jar kafka-spring-boot.jar [OPTIONS]");
        System.out.println("\nOptions:");
        System.out.println("  " + PRODUCER_ARG + "    Start producer mode to send messages");
        System.out.println("  " + CONSUMER_ARG + "    Start consumer mode to receive messages");
        System.out.println("  " + HELP_ARG + "       Show this help message");
        System.out.println("\nExamples:");
        System.out.println("  java -jar kafka-spring-boot.jar " + PRODUCER_ARG);
        System.out.println("  java -jar kafka-spring-boot.jar " + CONSUMER_ARG);
        System.out.println("  java -jar kafka-spring-boot.jar " + PRODUCER_ARG + " " + CONSUMER_ARG);
        System.out.println();
    }
}

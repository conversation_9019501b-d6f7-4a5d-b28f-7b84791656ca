package com.example.kafka_spring_boot.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;

/**
 * Configuration properties for Kafka application settings.
 */
@ConfigurationProperties(prefix = "app.kafka")
@Validated
public record KafkaProperties(
    @NotBlank String topicName,
    @Positive int messageCount,
    @NotBlank String defaultKey,
    @NotBlank String defaultValue
) {
    
    public KafkaProperties() {
        this("my-topic", 1000, "key", "value");
    }
}

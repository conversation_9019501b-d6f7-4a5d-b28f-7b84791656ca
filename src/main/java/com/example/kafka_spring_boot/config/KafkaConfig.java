package com.example.kafka_spring_boot.config;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

/**
 * Kafka configuration for topic creation and other Kafka-specific settings.
 */
@Configuration
public class KafkaConfig {

    private final KafkaProperties kafkaProperties;

    public KafkaConfig(KafkaProperties kafkaProperties) {
        this.kafkaProperties = kafkaProperties;
    }

    /**
     * Creates the Kafka topic if it doesn't exist.
     */
    @Bean
    public NewTopic createTopic() {
        return TopicBuilder.name(kafkaProperties.topicName())
                .partitions(3)
                .replicas(1)
                .build();
    }
}

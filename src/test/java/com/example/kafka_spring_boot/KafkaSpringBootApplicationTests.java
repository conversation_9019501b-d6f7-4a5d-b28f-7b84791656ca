package com.example.kafka_spring_boot;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.KafkaTemplate;

import com.example.kafka_spring_boot.config.KafkaProperties;
import com.example.kafka_spring_boot.service.ApplicationRunnerService;
import com.example.kafka_spring_boot.service.KafkaConsumerService;
import com.example.kafka_spring_boot.service.KafkaProducerService;

@SpringBootTest
class KafkaSpringBootApplicationTests {

	@MockBean
	private KafkaTemplate<String, String> kafkaTemplate;

	@MockBean
	private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

	@Autowired
	private KafkaProducerService producerService;

	@Autowired
	private KafkaConsumerService consumerService;

	@Autowired
	private ApplicationRunnerService applicationRunnerService;

	@Autowired
	private KafkaProperties kafkaProperties;

	@Test
	void contextLoads() {
		assertNotNull(producerService);
		assertNotNull(consumerService);
		assertNotNull(applicationRunnerService);
		assertNotNull(kafkaProperties);
	}
}

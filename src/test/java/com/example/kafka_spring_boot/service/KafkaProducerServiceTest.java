package com.example.kafka_spring_boot.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.concurrent.CompletableFuture;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.TopicPartition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import com.example.kafka_spring_boot.config.KafkaProperties;

@ExtendWith(MockitoExtension.class)
class KafkaProducerServiceTest {

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    private KafkaProperties kafkaProperties;
    private KafkaProducerService producerService;

    @BeforeEach
    void setUp() {
        kafkaProperties = new KafkaProperties("test-topic", 5, "test-key", "test-value");
        producerService = new KafkaProducerService(kafkaTemplate, kafkaProperties);
    }

    @Test
    void testSendMessage() {
        // Given
        String key = "test-key";
        String value = "test-value";
        
        ProducerRecord<String, String> producerRecord = new ProducerRecord<>("test-topic", key, value);
        RecordMetadata recordMetadata = new RecordMetadata(
            new TopicPartition("test-topic", 0), 0, 0, 0, 0, 0
        );
        SendResult<String, String> sendResult = new SendResult<>(producerRecord, recordMetadata);
        CompletableFuture<SendResult<String, String>> future = CompletableFuture.completedFuture(sendResult);

        when(kafkaTemplate.send(eq("test-topic"), eq(key), eq(value))).thenReturn(future);

        // When
        producerService.sendMessage(key, value);

        // Then
        verify(kafkaTemplate).send("test-topic", key, value);
    }

    @Test
    void testSendMessages() {
        // Given
        int count = 3;
        
        ProducerRecord<String, String> producerRecord = new ProducerRecord<>("test-topic", "key", "value");
        RecordMetadata recordMetadata = new RecordMetadata(
            new TopicPartition("test-topic", 0), 0, 0, 0, 0, 0
        );
        SendResult<String, String> sendResult = new SendResult<>(producerRecord, recordMetadata);
        CompletableFuture<SendResult<String, String>> future = CompletableFuture.completedFuture(sendResult);

        when(kafkaTemplate.send(eq("test-topic"), any(String.class), any(String.class))).thenReturn(future);

        // When
        producerService.sendMessages(count);

        // Then
        verify(kafkaTemplate).send(eq("test-topic"), eq("test-key-0"), eq("test-value-0"));
        verify(kafkaTemplate).send(eq("test-topic"), eq("test-key-1"), eq("test-value-1"));
        verify(kafkaTemplate).send(eq("test-topic"), eq("test-key-2"), eq("test-value-2"));
    }
}

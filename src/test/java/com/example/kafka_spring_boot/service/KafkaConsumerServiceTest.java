package com.example.kafka_spring_boot.service;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;

import com.example.kafka_spring_boot.config.KafkaProperties;

@ExtendWith(MockitoExtension.class)
class KafkaConsumerServiceTest {

    @Mock
    private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

    @Mock
    private MessageListenerContainer messageListenerContainer;

    private KafkaProperties kafkaProperties;
    private KafkaConsumerService consumerService;

    @BeforeEach
    void setUp() {
        kafkaProperties = new KafkaProperties("test-topic", 5, "test-key", "test-value");
        consumerService = new KafkaConsumerService(kafkaListenerEndpointRegistry, kafkaProperties);
    }

    @Test
    void testStartConsumer() {
        // Given
        when(kafkaListenerEndpointRegistry.getListenerContainer("kafkaConsumer"))
            .thenReturn(messageListenerContainer);
        when(messageListenerContainer.isRunning()).thenReturn(false);

        // When
        consumerService.startConsumer();

        // Then
        verify(messageListenerContainer).start();
    }

    @Test
    void testStopConsumer() {
        // Given
        when(kafkaListenerEndpointRegistry.getListenerContainer("kafkaConsumer"))
            .thenReturn(messageListenerContainer);
        when(messageListenerContainer.isRunning()).thenReturn(true);

        // When
        consumerService.stopConsumer();

        // Then
        verify(messageListenerContainer).stop();
    }

    @Test
    void testIsConsumerRunning_WhenRunning() {
        // Given
        when(kafkaListenerEndpointRegistry.getListenerContainer("kafkaConsumer"))
            .thenReturn(messageListenerContainer);
        when(messageListenerContainer.isRunning()).thenReturn(true);

        // When
        boolean isRunning = consumerService.isConsumerRunning();

        // Then
        assertTrue(isRunning);
    }

    @Test
    void testIsConsumerRunning_WhenNotRunning() {
        // Given
        when(kafkaListenerEndpointRegistry.getListenerContainer("kafkaConsumer"))
            .thenReturn(messageListenerContainer);
        when(messageListenerContainer.isRunning()).thenReturn(false);

        // When
        boolean isRunning = consumerService.isConsumerRunning();

        // Then
        assertFalse(isRunning);
    }

    @Test
    void testListen() {
        // Given
        String message = "test-message";
        String key = "test-key";
        String topic = "test-topic";
        int partition = 0;
        long offset = 123L;

        // When
        consumerService.listen(message, key, topic, partition, offset);

        // Then
        // This test mainly verifies that the method doesn't throw an exception
        // In a real scenario, you might want to verify logging or message processing
    }
}
